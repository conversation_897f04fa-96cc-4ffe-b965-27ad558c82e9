<odoo>
    <data>
        <record id = "stock_quant_views_factor" model="ir.ui.view">
            <field name="name">stock.quant.views.factor</field>
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="ro_meter" optional="show" sum="Total Meters"/>
                </xpath>
            </field>
        </record>

        <!-- <record id = "view_stock_quant_tree_simple" model="ir.ui.view">
            <field name="name">stock.quant.views.factor</field>
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
            <field name="arch" type="xml">
                <xpath expr="//list" position="inside">
                    <field name="ro_meter" optional="show"  column_invisible="not product_id.ro_has_factor if product_id else True" sum="Total Meters"/>
                </xpath>
            </field>
        </record>


        <record id = "view_stock_quant_tree" model="ir.ui.view">
            <field name="name">stock.quant.views.factor</field>
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
            <field name="arch" type="xml">
                <xpath expr="//list" position="inside">
                    <field name="ro_meter" optional="show"  column_invisible="not product_id.ro_has_factor if product_id else True" sum="Total Meters"/>
                </xpath>
            </field>
        </record> -->
    </data>
</odoo>