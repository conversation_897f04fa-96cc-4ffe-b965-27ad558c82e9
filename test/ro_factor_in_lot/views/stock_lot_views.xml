<odoo>
    <data>
        <record id = "stock_lot_views_factor" model="ir.ui.view">
            <field name="name">stock.lot.views.factor</field>
            <field name="model">stock.lot</field>
            <field name="inherit_id" ref="stock.view_production_lot_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='main_group']/group[1]" position="inside">
                    <field name="ro_height" invisible="not product_id.ro_has_factor"/>
                    <field name="ro_width" invisible="not product_id.ro_has_factor"/>
                    <field name="ro_factor" invisible="not product_id.ro_has_factor"/>
                </xpath>
                <xpath expr="//group[@name='main_group']/group[2]" position="inside">
                    <field name="ro_meter" invisible="not product_id.ro_has_factor"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>